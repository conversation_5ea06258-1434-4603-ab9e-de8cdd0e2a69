2025-07-16 11:43:17.806 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-16 11:43:17.811 | Loaded RSA public key for plugin verification
2025-07-16 11:43:17.926 | GitHub repositories enabled in configuration
2025-07-16 11:43:17.934 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-16 11:43:17.934 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 11:43:17.934 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-16 11:43:17.935 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 11:43:17.937 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-16 11:43:17.937 | Using Consul URL: consul:8500
2025-07-16 11:43:17.975 | Successfully initialized repository of type: local
2025-07-16 11:43:17.976 | Successfully initialized repository of type: mongo
2025-07-16 11:43:17.976 | Successfully initialized repository of type: librarian-definition
2025-07-16 11:43:17.977 | Successfully initialized repository of type: git
2025-07-16 11:43:17.977 | Initializing GitHub repository with provided credentials
2025-07-16 11:43:17.978 | GitHubRepository: Initialized for cpravetz/s7plugins. Plugins dir: 'plugins'. Default branch from config/env: main
2025-07-16 11:43:17.978 | Successfully initialized repository of type: github
2025-07-16 11:43:17.978 | Refreshing plugin cache...
2025-07-16 11:43:17.978 | Loading plugins from local repository...
2025-07-16 11:43:17.979 | LocalRepo: Loading fresh plugin list
2025-07-16 11:43:17.979 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-16 11:43:17.982 | Refreshing plugin cache...
2025-07-16 11:43:17.982 | Loading plugins from local repository...
2025-07-16 11:43:17.982 | LocalRepo: Loading fresh plugin list
2025-07-16 11:43:17.982 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-16 11:43:17.990 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-16 11:43:18.007 | LocalRepo: Loading from  [
2025-07-16 11:43:18.007 |   'ACCOMPLISH',
2025-07-16 11:43:18.007 |   'API_CLIENT',
2025-07-16 11:43:18.007 |   'CHAT',
2025-07-16 11:43:18.007 |   'CODE_EXECUTOR',
2025-07-16 11:43:18.007 |   'DATA_TOOLKIT',
2025-07-16 11:43:18.007 |   'FILE_OPS_PYTHON',
2025-07-16 11:43:18.007 |   'GET_USER_INPUT',
2025-07-16 11:43:18.007 |   'SCRAPE',
2025-07-16 11:43:18.007 |   'SEARCH_PYTHON',
2025-07-16 11:43:18.007 |   'TASK_MANAGER',
2025-07-16 11:43:18.007 |   'TEXT_ANALYSIS',
2025-07-16 11:43:18.007 |   'WEATHER'
2025-07-16 11:43:18.007 | ]
2025-07-16 11:43:18.007 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:18.008 | LocalRepo: Loading from  [
2025-07-16 11:43:18.008 |   'ACCOMPLISH',
2025-07-16 11:43:18.008 |   'API_CLIENT',
2025-07-16 11:43:18.008 |   'CHAT',
2025-07-16 11:43:18.008 |   'CODE_EXECUTOR',
2025-07-16 11:43:18.008 |   'DATA_TOOLKIT',
2025-07-16 11:43:18.008 |   'FILE_OPS_PYTHON',
2025-07-16 11:43:18.008 |   'GET_USER_INPUT',
2025-07-16 11:43:18.008 |   'SCRAPE',
2025-07-16 11:43:18.008 |   'SEARCH_PYTHON',
2025-07-16 11:43:18.008 |   'TASK_MANAGER',
2025-07-16 11:43:18.008 |   'TEXT_ANALYSIS',
2025-07-16 11:43:18.008 |   'WEATHER'
2025-07-16 11:43:18.008 | ]
2025-07-16 11:43:18.009 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:18.034 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-16 11:43:18.057 | Service CapabilitiesManager registered with Consul
2025-07-16 11:43:18.058 | Successfully registered CapabilitiesManager with Consul
2025-07-16 11:43:18.059 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 11:43:18.059 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 11:43:18.062 | CapabilitiesManager registered successfully with PostOffice
2025-07-16 11:43:18.063 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 11:43:18.064 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 11:43:18.065 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 11:43:18.065 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 11:43:18.067 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 11:43:18.067 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 11:43:18.069 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 11:43:18.069 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 11:43:18.071 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 11:43:18.071 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 11:43:18.073 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 11:43:18.073 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 11:43:18.075 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 11:43:18.075 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 11:43:18.077 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 11:43:18.077 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 11:43:18.079 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 11:43:18.079 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 11:43:18.081 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 11:43:18.081 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 11:43:18.083 | LocalRepo: Locators count 12
2025-07-16 11:43:18.084 | LocalRepo: Locators count 12
2025-07-16 11:43:18.085 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:18.085 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:18.086 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 11:43:18.086 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 11:43:18.087 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 11:43:18.087 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 11:43:18.088 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 11:43:18.088 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 11:43:18.090 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 11:43:18.090 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 11:43:18.093 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 11:43:18.094 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 11:43:18.098 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 11:43:18.098 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 11:43:18.098 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 11:43:18.099 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 11:43:18.099 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 11:43:18.100 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 11:43:18.100 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 11:43:18.100 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 11:43:18.101 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 11:43:18.101 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 11:43:18.102 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 11:43:18.102 | Loaded 12 plugins from local repository
2025-07-16 11:43:18.102 | Loading plugins from mongo repository...
2025-07-16 11:43:18.107 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 11:43:18.108 | Loaded 12 plugins from local repository
2025-07-16 11:43:18.109 | Loading plugins from mongo repository...
2025-07-16 11:43:19.916 | Loaded 0 plugins from mongo repository
2025-07-16 11:43:19.925 | Loading plugins from librarian-definition repository...
2025-07-16 11:43:19.936 | Loaded 0 plugins from mongo repository
2025-07-16 11:43:19.936 | Loading plugins from librarian-definition repository...
2025-07-16 11:43:19.978 | Loaded 0 plugins from librarian-definition repository
2025-07-16 11:43:19.978 | Loading plugins from git repository...
2025-07-16 11:43:19.987 | Loaded 0 plugins from librarian-definition repository
2025-07-16 11:43:19.987 | Loading plugins from git repository...
2025-07-16 11:43:20.041 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-16 11:43:20.041 | fatal: cannot copy '/usr/share/git-core/templates/info/exclude' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/info/exclude': File exists
2025-07-16 11:43:20.041 | 
2025-07-16 11:43:20.045 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-16 11:43:20.045 | fatal: cannot copy '/usr/share/git-core/templates/hooks/pre-merge-commit.sample' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/hooks/pre-merge-commit.sample': No such file or directory
2025-07-16 11:43:20.045 | 
2025-07-16 11:43:20.064 | Loaded 0 plugins from git repository
2025-07-16 11:43:20.064 | Loading plugins from github repository...
2025-07-16 11:43:20.225 | Loaded 0 plugins from git repository
2025-07-16 11:43:20.225 | Loading plugins from github repository...
2025-07-16 11:43:20.439 | Loaded 0 plugins from github repository
2025-07-16 11:43:20.439 | Plugin cache refreshed. Total plugins: 12
2025-07-16 11:43:20.439 | PluginRegistry initialized and cache populated.
2025-07-16 11:43:20.439 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 11:43:20.439 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 11:43:20.439 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 11:43:20.439 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 11:43:20.439 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-16 11:43:20.439 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-16 11:43:20.439 |     at async CapabilitiesManager.initialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:81:21)
2025-07-16 11:43:20.439 |     at async tryInitialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:57:17)
2025-07-16 11:43:20.439 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 11:43:20.440 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-16 11:43:20.440 |   'ACCOMPLISH',
2025-07-16 11:43:20.440 |   'API_CLIENT',
2025-07-16 11:43:20.440 |   'CHAT',
2025-07-16 11:43:20.440 |   'RUN_CODE',
2025-07-16 11:43:20.440 |   'DATA_TOOLKIT',
2025-07-16 11:43:20.440 |   'FILE_OPERATION',
2025-07-16 11:43:20.440 |   'ASK_USER_QUESTION',
2025-07-16 11:43:20.440 |   'SCRAPE',
2025-07-16 11:43:20.440 |   'SEARCH',
2025-07-16 11:43:20.440 |   'TASK_MANAGER',
2025-07-16 11:43:20.440 |   'TEXT_ANALYSIS',
2025-07-16 11:43:20.440 |   'WEATHER'
2025-07-16 11:43:20.440 | ]
2025-07-16 11:43:20.440 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-16 11:43:20.440 |   'plugin-ACCOMPLISH',
2025-07-16 11:43:20.440 |   'plugin-API_CLIENT',
2025-07-16 11:43:20.440 |   'plugin-CHAT',
2025-07-16 11:43:20.440 |   'plugin-CODE_EXECUTOR',
2025-07-16 11:43:20.440 |   'plugin-DATA_TOOLKIT',
2025-07-16 11:43:20.440 |   'plugin-FILE_OPS_PYTHON',
2025-07-16 11:43:20.440 |   'plugin-ASK_USER_QUESTION',
2025-07-16 11:43:20.440 |   'plugin-SCRAPE',
2025-07-16 11:43:20.440 |   'plugin-SEARCH_PYTHON',
2025-07-16 11:43:20.440 |   'plugin-TASK_MANAGER',
2025-07-16 11:43:20.440 |   'plugin-TEXT_ANALYSIS',
2025-07-16 11:43:20.440 |   'plugin-WEATHER'
2025-07-16 11:43:20.440 | ]
2025-07-16 11:43:20.440 | [CapabilitiesManager-constructor-ba120168] CapabilitiesManager.initialize: PluginRegistry initialized.
2025-07-16 11:43:20.441 | [CapabilitiesManager-constructor-ba120168] CapabilitiesManager.initialize: ConfigManager initialized.
2025-07-16 11:43:20.441 | [CapabilitiesManager-constructor-ba120168] Setting up express server...
2025-07-16 11:43:20.453 | [CapabilitiesManager-constructor-ba120168] CapabilitiesManager server listening on port 5060
2025-07-16 11:43:20.454 | [CapabilitiesManager-constructor-ba120168] CapabilitiesManager server setup complete
2025-07-16 11:43:20.454 | [CapabilitiesManager-constructor-ba120168] CapabilitiesManager.initialize: CapabilitiesManager initialization completed.
2025-07-16 11:43:20.455 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 11:43:20.455 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 11:43:20.455 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 11:43:20.456 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 11:43:20.456 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-16 11:43:20.456 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-16 11:43:20.456 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 11:43:20.456 | Loaded 0 plugins from github repository
2025-07-16 11:43:20.456 | Plugin cache refreshed. Total plugins: 12
2025-07-16 11:43:20.456 | PluginRegistry initialized and cache populated.
2025-07-16 11:43:20.456 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-16 11:43:20.456 |   'ACCOMPLISH',
2025-07-16 11:43:20.456 |   'API_CLIENT',
2025-07-16 11:43:20.456 |   'CHAT',
2025-07-16 11:43:20.456 |   'RUN_CODE',
2025-07-16 11:43:20.456 |   'DATA_TOOLKIT',
2025-07-16 11:43:20.456 |   'FILE_OPERATION',
2025-07-16 11:43:20.456 |   'ASK_USER_QUESTION',
2025-07-16 11:43:20.456 |   'SCRAPE',
2025-07-16 11:43:20.456 |   'SEARCH',
2025-07-16 11:43:20.456 |   'TASK_MANAGER',
2025-07-16 11:43:20.456 |   'TEXT_ANALYSIS',
2025-07-16 11:43:20.456 |   'WEATHER'
2025-07-16 11:43:20.456 | ]
2025-07-16 11:43:20.456 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-16 11:43:20.456 |   'plugin-ACCOMPLISH',
2025-07-16 11:43:20.456 |   'plugin-API_CLIENT',
2025-07-16 11:43:20.456 |   'plugin-CHAT',
2025-07-16 11:43:20.456 |   'plugin-CODE_EXECUTOR',
2025-07-16 11:43:20.456 |   'plugin-DATA_TOOLKIT',
2025-07-16 11:43:20.456 |   'plugin-FILE_OPS_PYTHON',
2025-07-16 11:43:20.456 |   'plugin-ASK_USER_QUESTION',
2025-07-16 11:43:20.456 |   'plugin-SCRAPE',
2025-07-16 11:43:20.456 |   'plugin-SEARCH_PYTHON',
2025-07-16 11:43:20.456 |   'plugin-TASK_MANAGER',
2025-07-16 11:43:20.456 |   'plugin-TEXT_ANALYSIS',
2025-07-16 11:43:20.456 |   'plugin-WEATHER'
2025-07-16 11:43:20.456 | ]
2025-07-16 11:43:33.116 | Connected to RabbitMQ
2025-07-16 11:43:33.128 | Channel created successfully
2025-07-16 11:43:33.128 | RabbitMQ channel ready
2025-07-16 11:43:33.201 | Connection test successful - RabbitMQ connection is stable
2025-07-16 11:43:33.201 | Creating queue: capabilitiesmanager-CapabilitiesManager
2025-07-16 11:43:33.218 | Binding queue to exchange: stage7
2025-07-16 11:43:33.238 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-16 11:43:55.720 | Created ServiceTokenManager for CapabilitiesManager
2025-07-16 11:43:55.732 | In executeAccomplishPlugin
2025-07-16 11:43:55.733 | LocalRepo: Using cached plugin list
2025-07-16 11:43:55.734 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:55.735 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 11:43:55.736 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 11:43:55.738 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 11:43:55.739 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 11:43:55.741 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 11:43:55.742 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 11:43:55.743 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 11:43:55.745 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 11:43:55.746 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 11:43:55.747 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 11:43:55.748 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 11:43:56.530 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 11:43:56.530 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 11:43:56.530 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 11:43:56.530 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 11:43:56.530 |     at async PluginMarketplace.getAvailablePluginsStr (/usr/src/app/marketplace/dist/PluginMarketplace.js:356:34)
2025-07-16 11:43:56.530 |     at async PluginRegistry.getAvailablePluginsStr (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:302:20)
2025-07-16 11:43:56.530 |     at async CapabilitiesManager.executeAccomplishPlugin (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:1211:35)
2025-07-16 11:43:56.530 |     at async CapabilitiesManager.executeActionVerb (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:394:47)
2025-07-16 11:43:56.530 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 11:43:56.530 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executeAccomplishPlugin: Plugins string for ACCOMPLISH: - DELEGATE: Create sub-agents with goals of their own.
2025-07-16 11:43:56.530 | - THINK: - sends prompts to the chat function...
2025-07-16 11:43:56.530 | PluginRegistry.fetchOneByVerb called for verb: ACCOMPLISH
2025-07-16 11:43:56.532 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 11:43:56.533 | Using inline plugin path for plugin-ACCOMPLISH (ACCOMPLISH): /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 11:43:56.541 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePlugin: Executing plugin plugin-ACCOMPLISH v1.0.0 (ACCOMPLISH) at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 11:43:56.590 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePythonPlugin: Python execution - Main file path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py, Root path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 11:43:56.660 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Found python executable with command: python3 --version
2025-07-16 11:43:56.660 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Creating virtual environment at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv.
2025-07-16 11:43:56.660 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Running command: python3 -m venv "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv"
2025-07-16 11:44:04.258 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Upgrading pip with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install --upgrade pip
2025-07-16 11:44:09.086 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Installing requirements with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install -r "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt"
2025-07-16 11:44:16.640 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Python dependency installation stdout: Collecting requests>=2.28.0 (from -r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 11:44:16.640 |   Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
2025-07-16 11:44:16.640 | Collecting charset_normalizer<4,>=2 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 11:44:16.640 |   Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl.metadata (35 kB)
2025-07-16 11:44:16.640 | Collecting idna<4,>=2.5 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 11:44:16.640 |   Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
2025-07-16 11:44:16.640 | Collecting urllib3<3,>=1.21.1 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 11:44:16.640 |   Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
2025-07-16 11:44:16.640 | Collecting certifi>=2017.4.17 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 11:44:16.640 |   Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
2025-07-16 11:44:16.640 | Downloading requests-2.32.4-py3-none-any.whl (64 kB)
2025-07-16 11:44:16.640 | Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl (149 kB)
2025-07-16 11:44:16.640 | Downloading idna-3.10-py3-none-any.whl (70 kB)
2025-07-16 11:44:16.640 | Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
2025-07-16 11:44:16.640 | Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
2025-07-16 11:44:16.640 | Installing collected packages: urllib3, idna, charset_normalizer, certifi, requests
2025-07-16 11:44:16.640 | 
2025-07-16 11:44:16.640 | Successfully installed certifi-2025.7.14 charset_normalizer-3.4.2 idna-3.10 requests-2.32.4 urllib3-2.5.0
2025-07-16 11:44:16.640 | 
2025-07-16 11:44:16.641 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.ensurePythonDependencies: Python dependencies processed successfully for /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH. Marker file updated.
2025-07-16 11:44:16.642 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePythonPlugin: Executing Python command: echo "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" | base64 -d | "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/python" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH"
2025-07-16 11:44:16.642 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePythonPlugin: Piping inputsJsonString to Python plugin: [["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}],["verbToAvoid",{"inputName":"verbToAvoid","value":"EXECUTE","valueType":"string","args":{}}],["available_plugins",{"inputName":"available_plugins","value":"- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location","valueType":"string","args":{}}],["postOffice_url",{"inputName":"postOffice_url","value":"postoffice:5020","valueType":"string","args":{}}],["brain_url",{"inputName":"brain_url","value":"brain:5070","valueType":"string","args":{}}],["librarian_url",{"inputName":"librarian_url","value":"librarian:5040","valueType":"string","args":{}}],["__auth_token",{"inputName":"__auth_token","value":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["__brain_auth_token",{"inputName":"__brain_auth_token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["token",{"inputName":"token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}]]
2025-07-16 11:44:20.057 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePythonPlugin: Raw stderr from Python plugin ACCOMPLISH v1.0.0:
2025-07-16 11:44:20.057 | 2025-07-16 15:44:17,016 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}
2025-07-16 11:44:20.057 | 2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Using provided plugin context or fallback
2025-07-16 11:44:20.057 | 2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-16 11:44:20.057 | 2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-16 11:44:20.057 | 2025-07-16 15:44:18,619 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-16 11:44:20.057 | 2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-16 11:44:20.057 | 2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-16 11:44:20.057 | 2025-07-16 15:44:20,020 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-16 11:44:20.057 | 
2025-07-16 11:44:20.057 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.executePythonPlugin: Raw stdout from Python plugin ACCOMPLISH v1.0.0:
2025-07-16 11:44:20.057 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 15:44:17,016 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 15:44:18,619 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 15:44:20,020 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-16 11:44:20.057 | 
2025-07-16 11:44:20.058 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.validatePythonOutput: Validating Python output for ACCOMPLISH v1.0.0. Received stdout:
2025-07-16 11:44:20.058 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 15:44:17,016 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 15:44:17,016 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 15:44:18,619 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 15:44:18,620 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 15:44:20,020 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-16 11:44:20.058 | 
2025-07-16 11:44:20.058 | [c06b4408-803e-4531-bd72-1ad503d5523a] CapabilitiesManager.validatePythonOutput: Python plugin output parsed and validated successfully for ACCOMPLISH v1.0.0