import { BaseModel, ModelScore } from './baseModel';
import { LLMConversationType } from '../interfaces/baseInterface';

export class Claude2Model extends BaseModel {
    constructor() {
        const scoresByConversationType = new Map<LLMConversationType, ModelScore>([
            [LLMConversationType.TextToText, {
                costScore: 10,  // Lowered to avoid using this model when connection fails
                accuracyScore: 40,  // Lowered to avoid using this model when connection fails
                creativityScore: 40,  // Lowered to avoid using this model when connection fails
                speedScore: 30  // Lowered to avoid using this model when connection fails
            }],
            [LLMConversationType.TextToCode, {
                costScore: 10,  // Lowered to avoid using this model when connection fails
                accuracyScore: 40,  // Lowered to avoid using this model when connection fails
                creativityScore: 40,  // Lowered to avoid using this model when connection fails
                speedScore: 30  // Lowered to avoid using this model when connection fails
            }],
            [LLMConversationType.CodeToText, {
                costScore: 10,  // Lowered to avoid using this model when connection fails
                accuracyScore: 40,  // Lowered to avoid using this model when connection fails
                creativityScore: 40,  // Lowered to avoid using this model when connection fails
                speedScore: 30  // Lowered to avoid using this model when connection fails
            }]
        ]);

        super({
            name: "anthropic/claude-2",
            modelName: "anthropic/claude-2",
            interfaceName: "openrouter",
            serviceName: "ORService",
            tokenLimit: 100000, // Adjust this value if you know the exact token limit for Claude 2
            scoresByConversationType: scoresByConversationType,
            contentConversation: [LLMConversationType.TextToCode, LLMConversationType.CodeToText, LLMConversationType.TextToText]
        });
    }
}

const aiModel = new Claude2Model();
export default aiModel;