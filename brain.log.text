2025-07-16 11:43:15.214 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-16 11:43:15.263 | Loaded RSA public key for plugin verification
2025-07-16 11:43:15.318 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-16 11:43:15.318 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 11:43:15.320 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-16 11:43:15.320 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 11:43:15.321 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-16 11:43:15.323 | Using Consul URL: consul:8500
2025-07-16 11:43:15.416 | Brain service listening at http://0.0.0.0:5070
2025-07-16 11:43:15.418 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-16 11:43:15.453 | Anthropic Service created, <PERSON><PERSON><PERSON><PERSON> starts sk-ant
2025-07-16 11:43:15.456 | Loaded service: AntService
2025-07-16 11:43:15.457 | GG Gemini Service created, ApiKey starts AIzaSy
2025-07-16 11:43:15.457 | Loaded service: GGService
2025-07-16 11:43:15.493 | Gemini Service created, ApiKey starts AIzaSy
2025-07-16 11:43:15.504 | Loaded service: gemini
2025-07-16 11:43:15.524 | Groq Service created, ApiKey starts gsk_m0
2025-07-16 11:43:15.524 | GroqService initialized with API key: Set (length: 56)
2025-07-16 11:43:15.524 | Loaded service: groq
2025-07-16 11:43:15.527 | Huggingface Service created with API key: Set (length: 37)
2025-07-16 11:43:15.534 | Loaded service: HFService
2025-07-16 11:43:15.535 | Mistral Service created, ApiKey starts AhDwC8
2025-07-16 11:43:15.536 | Loaded service: MistralService
2025-07-16 11:43:15.543 | OpenAI Service created, ApiKey starts sk-LaE
2025-07-16 11:43:15.543 | Loaded service: OAService
2025-07-16 11:43:15.552 | OpenRouter Service created, ApiKey starts sk-or-
2025-07-16 11:43:15.552 | Loaded service: ORService
2025-07-16 11:43:15.554 | Openweb Service created, ApiKey starts eyJhbG
2025-07-16 11:43:15.554 | Using default OpenWebUI URL: https://knllm.dusdusdusd.com
2025-07-16 11:43:15.554 | Loaded service: OWService
2025-07-16 11:43:15.560 | modelManager Loaded 9 services.
2025-07-16 11:43:15.560 | Loaded interface: anthropic
2025-07-16 11:43:15.560 | Loaded interface: gemini
2025-07-16 11:43:16.371 | Loaded interface: groq
2025-07-16 11:43:16.391 | Loaded interface: huggingface
2025-07-16 11:43:16.392 | Loaded interface: mistral
2025-07-16 11:43:16.393 | Loaded interface: openai
2025-07-16 11:43:16.396 | Loaded interface: openrouter
2025-07-16 11:43:16.396 | OpenWebUIInterface initialized with DEFAULT_TIMEOUT: 300000ms
2025-07-16 11:43:16.396 | Loaded interface: openwebui
2025-07-16 11:43:16.396 | modelManager Loaded 8 interfaces.
2025-07-16 11:43:16.412 | Loaded model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:16.412 | Loaded model: suno/bark
2025-07-16 11:43:16.422 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-16 11:43:16.423 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-16 11:43:16.439 | Loaded model: anthropic/claude-2
2025-07-16 11:43:16.440 | Loaded model: codellama/CodeLlama-34b-Instruct-hf
2025-07-16 11:43:16.441 | Loaded model: THUDM/cogvlm-chat-hf
2025-07-16 11:43:16.443 | Loaded model: openai/dall-e-2
2025-07-16 11:43:16.458 | Loaded model: openai/dall-e-3
2025-07-16 11:43:16.471 | Loaded model: deepseek-ai/DeepSeek-R1
2025-07-16 11:43:16.478 | Loaded model: openai/whisper-large-v3
2025-07-16 11:43:16.478 | Loaded model: google/gemini-2.0-flash-lite
2025-07-16 11:43:16.478 | Loaded model: google/gemini-2.5-flash
2025-07-16 11:43:16.478 | Loaded model: google/gemma-3-27b-it
2025-07-16 11:43:16.480 | Loaded model: openai/gpt-4.1-nano
2025-07-16 11:43:16.480 | Loaded model: openai/gpt-4-vision-preview
2025-07-16 11:43:16.480 | Loaded model: nousresearch/hermes-3-llama-3.1-405b
2025-07-16 11:43:16.480 | KNLLMModel initialized with OpenWebUI interface
2025-07-16 11:43:16.480 | Loaded model: openweb/knownow
2025-07-16 11:43:16.481 | Loaded model: liquid/lfm-40b
2025-07-16 11:43:16.483 | Loaded model: meta-llama/llama-3.2-11b-vision-instruct
2025-07-16 11:43:16.485 | GroqService availability check: Available
2025-07-16 11:43:16.485 | GroqService API key: Set (length: 56)
2025-07-16 11:43:16.485 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:16.485 | GroqService ready state: Ready
2025-07-16 11:43:16.485 | GroqService is available and ready to use.
2025-07-16 11:43:16.485 | Loaded model: groq/llama-4
2025-07-16 11:43:16.499 | Loaded model: meta-llama/Llama-2-70b-chat-hf
2025-07-16 11:43:16.528 | Loaded model: liuhaotian/llava-v1.5-13b
2025-07-16 11:43:16.528 | Loaded model: microsoft/Phi-3.5-vision-instruct
2025-07-16 11:43:16.543 | MistralService availability check: Available
2025-07-16 11:43:16.543 | MistralService API key: Set
2025-07-16 11:43:16.543 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:16.543 | MistralService is available and ready to use.
2025-07-16 11:43:16.543 | Loaded model: mistral/mistral-small-latest
2025-07-16 11:43:16.627 | Loaded model: mistralai/Mistral-Nemo-Instruct-2407
2025-07-16 11:43:16.634 | Loaded model: facebook/musicgen-large
2025-07-16 11:43:16.686 | MistralService availability check: Available
2025-07-16 11:43:16.686 | MistralService API key: Set
2025-07-16 11:43:16.686 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:16.686 | MistralService is available and ready to use.
2025-07-16 11:43:16.686 | Loaded model: mistral/pixtral-12B-2409
2025-07-16 11:43:16.703 | GroqService availability check: Available
2025-07-16 11:43:16.703 | GroqService API key: Set (length: 56)
2025-07-16 11:43:16.703 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:16.703 | GroqService ready state: Ready
2025-07-16 11:43:16.703 | GroqService is available and ready to use.
2025-07-16 11:43:16.703 | Loaded model: groq/qwen-qwq-32b
2025-07-16 11:43:16.703 | Loaded model: facebook/seamless-m4t-large
2025-07-16 11:43:16.720 | Loaded model: stabilityai/stable-diffusion-xl-base-1.0
2025-07-16 11:43:16.720 | Loaded model: bigcode/starcoder
2025-07-16 11:43:16.727 | Loaded model: openai/tts
2025-07-16 11:43:16.734 | Loaded model: openai/whisper-large-v3
2025-07-16 11:43:16.734 | Loaded model: openai/whisper
2025-07-16 11:43:16.734 | modelManager Loaded 33 models.
2025-07-16 11:43:16.777 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-16 11:43:16.786 | Service Brain registered with Consul
2025-07-16 11:43:16.786 | Successfully registered Brain with Consul
2025-07-16 11:43:16.823 | Brain registered successfully with PostOffice
2025-07-16 11:43:16.826 | [Brain] Attempting to restore model performance data from Librarian...
2025-07-16 11:43:16.851 | [AuthenticatedAxios] Request rog9t83s82: Failed after 8ms: {
2025-07-16 11:43:16.851 |   status: undefined,
2025-07-16 11:43:16.851 |   statusText: undefined,
2025-07-16 11:43:16.851 |   data: undefined,
2025-07-16 11:43:16.851 |   url: 'http://librarian:5040/loadData/model-performance-data'
2025-07-16 11:43:16.851 | }
2025-07-16 11:43:17.916 | [Brain] Successfully restored 33 model performance records from Librarian
2025-07-16 11:43:20.510 | Created ServiceTokenManager for Brain
2025-07-16 11:43:36.868 | Connected to RabbitMQ
2025-07-16 11:43:36.879 | Channel created successfully
2025-07-16 11:43:36.879 | RabbitMQ channel ready
2025-07-16 11:43:36.952 | Connection test successful - RabbitMQ connection is stable
2025-07-16 11:43:36.952 | Creating queue: brain-Brain
2025-07-16 11:43:36.966 | Binding queue to exchange: stage7
2025-07-16 11:43:36.984 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-16 11:43:53.822 | [Brain Chat] Request 6cf6e90e-047c-4fae-a6c6-5dc36b0aa3ee received
2025-07-16 11:43:53.823 | Selecting model for optimization: accuracy, conversationType: TextToText
2025-07-16 11:43:53.824 | **** CACHE MISS **** No cached result for key: accuracy-TextToText
2025-07-16 11:43:53.824 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:43:53.826 | Total models loaded: 33
2025-07-16 11:43:53.829 | GroqService availability check: Available
2025-07-16 11:43:53.829 | GroqService API key: Set (length: 56)
2025-07-16 11:43:53.829 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:53.829 | GroqService ready state: Ready
2025-07-16 11:43:53.829 | GroqService is available and ready to use.
2025-07-16 11:43:53.829 | MistralService availability check: Available
2025-07-16 11:43:53.829 | MistralService API key: Set
2025-07-16 11:43:53.829 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:53.829 | MistralService is available and ready to use.
2025-07-16 11:43:53.830 | MistralService availability check: Available
2025-07-16 11:43:53.830 | MistralService API key: Set
2025-07-16 11:43:53.830 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:53.830 | MistralService is available and ready to use.
2025-07-16 11:43:53.830 | GroqService availability check: Available
2025-07-16 11:43:53.830 | GroqService API key: Set (length: 56)
2025-07-16 11:43:53.830 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:53.830 | GroqService ready state: Ready
2025-07-16 11:43:53.830 | GroqService is available and ready to use.
2025-07-16 11:43:53.830 | Model hf/meta-llama/llama-3.2-3b-instruct score calculation: base=100, adjusted=100, reliability=0, final=100
2025-07-16 11:43:53.830 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:43:53.830 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:53.832 | Model google/gemini-2.0-flash-lite score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:53.832 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:53.832 | Model google/gemma-3-27b-it score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:53.832 | Model openai/gpt-4.1-nano score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:53.832 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:43:53.832 | Model nousresearch/hermes-3-llama-3.1-405b score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:53.832 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:53.832 | Model liquid/lfm-40b score calculation: base=75, adjusted=75, reliability=0, final=75
2025-07-16 11:43:53.832 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:53.832 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:53.832 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:53.832 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:53.832 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:53.832 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:53.832 | Using score-based model selection. Top model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:53.832 | Selected model hf/meta-llama/llama-3.2-3b-instruct for accuracy optimization and conversation type TextToText
2025-07-16 11:43:53.832 | [Brain Chat] Using model meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:53.832 | [ModelManager] Tracking model request: 0272ab7f-94bd-434a-8368-f3f8d1998b14 for model meta-llama/llama-3.2-3b-instruct, conversation type TextToText
2025-07-16 11:43:53.832 | [ModelManager] Active requests count: 1
2025-07-16 11:43:53.833 | Starting trimMessages
2025-07-16 11:43:53.833 | Token allocation: input=608, max_new=3288, total=3896
2025-07-16 11:43:54.163 | Error in Huggingface stream: Server response contains error: 404
2025-07-16 11:43:54.163 | Received 404 error from Huggingface model meta-llama/llama-3.2-3b-instruct, blacklisting temporarily.
2025-07-16 11:43:54.167 | Error generating response from Huggingface: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:54.169 | [Brain] Model meta-llama/llama-3.2-3b-instruct failed: Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:54.169 | No active request found for request ID 6cf6e90e-047c-4fae-a6c6-5dc36b0aa3ee
2025-07-16 11:43:54.169 | [ModelManager] Tracking model response for request 6cf6e90e-047c-4fae-a6c6-5dc36b0aa3ee, success: false, token count: 0, isRetry: undefined
2025-07-16 11:43:54.169 | Clearing model selection cache
2025-07-16 11:43:54.169 | Selecting model for optimization: accuracy, conversationType: TextToText
2025-07-16 11:43:54.169 | **** CACHE MISS **** No cached result for key: accuracy-TextToText
2025-07-16 11:43:54.169 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:43:54.169 | Total models loaded: 33
2025-07-16 11:43:54.170 | GroqService availability check: Available
2025-07-16 11:43:54.170 | GroqService API key: Set (length: 56)
2025-07-16 11:43:54.170 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:54.170 | GroqService ready state: Ready
2025-07-16 11:43:54.170 | GroqService is available and ready to use.
2025-07-16 11:43:54.170 | MistralService availability check: Available
2025-07-16 11:43:54.170 | MistralService API key: Set
2025-07-16 11:43:54.170 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:54.170 | MistralService is available and ready to use.
2025-07-16 11:43:54.170 | MistralService availability check: Available
2025-07-16 11:43:54.170 | MistralService API key: Set
2025-07-16 11:43:54.170 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:54.170 | MistralService is available and ready to use.
2025-07-16 11:43:54.170 | GroqService availability check: Available
2025-07-16 11:43:54.170 | GroqService API key: Set (length: 56)
2025-07-16 11:43:54.170 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:54.170 | GroqService ready state: Ready
2025-07-16 11:43:54.170 | GroqService is available and ready to use.
2025-07-16 11:43:54.170 | Model hf/meta-llama/llama-3.2-3b-instruct score calculation: base=100, adjusted=100, reliability=0, final=100
2025-07-16 11:43:54.170 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:43:54.170 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:54.170 | Model google/gemini-2.0-flash-lite score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:54.170 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:54.170 | Model google/gemma-3-27b-it score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:54.170 | Model openai/gpt-4.1-nano score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:54.170 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:43:54.170 | Model nousresearch/hermes-3-llama-3.1-405b score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:54.171 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:54.171 | Model liquid/lfm-40b score calculation: base=75, adjusted=75, reliability=0, final=75
2025-07-16 11:43:54.171 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:54.171 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:54.171 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:54.171 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:54.171 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:54.171 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:54.171 | Using score-based model selection. Top model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:54.171 | Selected model hf/meta-llama/llama-3.2-3b-instruct for accuracy optimization and conversation type TextToText
2025-07-16 11:43:54.171 | [Brain Chat] Error: Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:55.797 | [Brain Chat] Request bda35612-6a39-459d-a3dc-62ea3c342858 received
2025-07-16 11:43:55.797 | Selecting model for optimization: accuracy, conversationType: TextToText
2025-07-16 11:43:55.797 | **** CACHE HIT **** Using cached model selection result: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:55.797 | Cache age: 1 seconds
2025-07-16 11:43:55.797 | [Brain Chat] Using model meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:55.797 | [ModelManager] Tracking model request: a15c869f-66aa-4e0b-8c39-cfe1a1811caa for model meta-llama/llama-3.2-3b-instruct, conversation type TextToText
2025-07-16 11:43:55.797 | [ModelManager] Active requests count: 2
2025-07-16 11:43:55.797 | Starting trimMessages
2025-07-16 11:43:55.797 | Token allocation: input=764, max_new=3132, total=3896
2025-07-16 11:43:55.876 | Received 404 error from Huggingface model meta-llama/llama-3.2-3b-instruct, blacklisting temporarily.
2025-07-16 11:43:55.876 | Error in Huggingface stream: Server response contains error: 404
2025-07-16 11:43:55.877 | Error analysis already in progress, skipping
2025-07-16 11:43:55.877 | [ModelManager] Tracking model response for request bda35612-6a39-459d-a3dc-62ea3c342858, success: false, token count: 0, isRetry: undefined
2025-07-16 11:43:55.877 | Clearing model selection cache
2025-07-16 11:43:55.877 | Selecting model for optimization: accuracy, conversationType: TextToText
2025-07-16 11:43:55.877 | **** CACHE MISS **** No cached result for key: accuracy-TextToText
2025-07-16 11:43:55.877 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:43:55.877 | Total models loaded: 33
2025-07-16 11:43:55.877 | GroqService availability check: Available
2025-07-16 11:43:55.877 | GroqService API key: Set (length: 56)
2025-07-16 11:43:55.877 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:55.877 | GroqService ready state: Ready
2025-07-16 11:43:55.877 | GroqService is available and ready to use.
2025-07-16 11:43:55.877 | MistralService availability check: Available
2025-07-16 11:43:55.877 | MistralService API key: Set
2025-07-16 11:43:55.877 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:55.877 | MistralService is available and ready to use.
2025-07-16 11:43:55.877 | MistralService availability check: Available
2025-07-16 11:43:55.877 | Error generating response from Huggingface: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:55.877 | [Brain] Model meta-llama/llama-3.2-3b-instruct failed: Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:55.877 | No active request found for request ID bda35612-6a39-459d-a3dc-62ea3c342858
2025-07-16 11:43:55.880 | MistralService API key: Set
2025-07-16 11:43:55.880 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:43:55.880 | MistralService is available and ready to use.
2025-07-16 11:43:55.880 | GroqService availability check: Available
2025-07-16 11:43:55.880 | GroqService API key: Set (length: 56)
2025-07-16 11:43:55.880 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:43:55.880 | GroqService ready state: Ready
2025-07-16 11:43:55.880 | GroqService is available and ready to use.
2025-07-16 11:43:55.880 | Model hf/meta-llama/llama-3.2-3b-instruct score calculation: base=100, adjusted=100, reliability=0, final=100
2025-07-16 11:43:55.880 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:43:55.880 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:55.880 | Model google/gemini-2.0-flash-lite score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:55.880 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:55.880 | Model google/gemma-3-27b-it score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:55.880 | Model openai/gpt-4.1-nano score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:55.880 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:43:55.880 | Model nousresearch/hermes-3-llama-3.1-405b score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:55.880 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:55.880 | Model liquid/lfm-40b score calculation: base=75, adjusted=75, reliability=0, final=75
2025-07-16 11:43:55.880 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:55.880 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:55.880 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:43:55.880 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:43:55.880 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:43:55.880 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:43:55.880 | Using score-based model selection. Top model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 11:43:55.880 | Selected model hf/meta-llama/llama-3.2-3b-instruct for accuracy optimization and conversation type TextToText
2025-07-16 11:43:55.880 | [Brain Chat] Error: Error: Huggingface model meta-llama/llama-3.2-3b-instruct returned 404 and has been blacklisted temporarily.
2025-07-16 11:43:55.886 | Error analyzing error: Request failed with status code 500
2025-07-16 11:44:17.023 | [Brain Chat] Request dcc25c87-f510-4cbb-afa5-29e96a482ca3 received
2025-07-16 11:44:17.024 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 11:44:17.024 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 11:44:17.024 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:44:17.024 | Total models loaded: 33
2025-07-16 11:44:17.024 | GroqService availability check: Available
2025-07-16 11:44:17.024 | GroqService API key: Set (length: 56)
2025-07-16 11:44:17.024 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:17.024 | GroqService ready state: Ready
2025-07-16 11:44:17.024 | GroqService is available and ready to use.
2025-07-16 11:44:17.024 | MistralService availability check: Available
2025-07-16 11:44:17.024 | MistralService API key: Set
2025-07-16 11:44:17.024 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:17.024 | MistralService is available and ready to use.
2025-07-16 11:44:17.024 | MistralService availability check: Available
2025-07-16 11:44:17.024 | MistralService API key: Set
2025-07-16 11:44:17.024 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:17.024 | MistralService is available and ready to use.
2025-07-16 11:44:17.024 | GroqService availability check: Available
2025-07-16 11:44:17.024 | GroqService API key: Set (length: 56)
2025-07-16 11:44:17.024 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:17.024 | GroqService ready state: Ready
2025-07-16 11:44:17.024 | GroqService is available and ready to use.
2025-07-16 11:44:17.024 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:44:17.024 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:17.024 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:17.024 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:17.024 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:17.024 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:17.024 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:17.024 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:17.025 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:17.025 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:17.025 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:17.025 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:17.025 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:17.025 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:17.025 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:17.025 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:17.025 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 11:44:17.025 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 11:44:17.025 | [Brain Chat] Using model anthropic/claude-2
2025-07-16 11:44:17.025 | [ModelManager] Tracking model request: 793b13b6-e0b1-4bfa-87bc-a5a1f8f3abd7 for model anthropic/claude-2, conversation type TextToCode
2025-07-16 11:44:17.025 | [ModelManager] Active requests count: 3
2025-07-16 11:44:17.026 | Starting trimMessages
2025-07-16 11:44:18.615 | [ModelManager] Tracking model response for request dcc25c87-f510-4cbb-afa5-29e96a482ca3, success: false, token count: 0, isRetry: undefined
2025-07-16 11:44:18.615 | Clearing model selection cache
2025-07-16 11:44:18.615 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 11:44:18.615 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 11:44:18.615 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:44:18.615 | Total models loaded: 33
2025-07-16 11:44:18.615 | [Brain] Model anthropic/claude-2 failed: Connection error.
2025-07-16 11:44:18.615 | No active request found for request ID dcc25c87-f510-4cbb-afa5-29e96a482ca3
2025-07-16 11:44:18.616 | GroqService availability check: Available
2025-07-16 11:44:18.616 | GroqService API key: Set (length: 56)
2025-07-16 11:44:18.616 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:18.616 | GroqService ready state: Ready
2025-07-16 11:44:18.616 | GroqService is available and ready to use.
2025-07-16 11:44:18.616 | MistralService availability check: Available
2025-07-16 11:44:18.616 | MistralService API key: Set
2025-07-16 11:44:18.616 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:18.616 | MistralService is available and ready to use.
2025-07-16 11:44:18.616 | MistralService availability check: Available
2025-07-16 11:44:18.616 | MistralService API key: Set
2025-07-16 11:44:18.616 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:18.616 | MistralService is available and ready to use.
2025-07-16 11:44:18.616 | GroqService availability check: Available
2025-07-16 11:44:18.616 | GroqService API key: Set (length: 56)
2025-07-16 11:44:18.616 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:18.616 | GroqService ready state: Ready
2025-07-16 11:44:18.616 | GroqService is available and ready to use.
2025-07-16 11:44:18.616 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:44:18.616 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:18.616 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:18.616 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:18.616 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:18.616 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:18.617 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:18.617 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:18.617 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:18.617 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:18.617 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:18.617 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:18.617 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:18.617 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:18.617 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:18.617 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:18.617 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 11:44:18.617 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 11:44:18.617 | [Brain Chat] Error: Connection error.
2025-07-16 11:44:18.625 | [Brain Chat] Request fe196a0d-8d63-4fe6-8260-56d9ea525082 received
2025-07-16 11:44:18.625 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 11:44:18.625 | **** CACHE HIT **** Using cached model selection result: anthropic/claude-2
2025-07-16 11:44:18.625 | Cache age: 0 seconds
2025-07-16 11:44:18.625 | [Brain Chat] Using model anthropic/claude-2
2025-07-16 11:44:18.625 | [ModelManager] Tracking model request: 06a86567-726e-43c6-8fc0-a357449ae3ce for model anthropic/claude-2, conversation type TextToCode
2025-07-16 11:44:18.625 | [ModelManager] Active requests count: 4
2025-07-16 11:44:18.625 | Starting trimMessages
2025-07-16 11:44:20.017 | [ModelManager] Tracking model response for request fe196a0d-8d63-4fe6-8260-56d9ea525082, success: false, token count: 0, isRetry: undefined
2025-07-16 11:44:20.017 | Clearing model selection cache
2025-07-16 11:44:20.017 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 11:44:20.017 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 11:44:20.017 | Cache miss or expired. Selecting model from scratch.
2025-07-16 11:44:20.017 | Total models loaded: 33
2025-07-16 11:44:20.017 | GroqService availability check: Available
2025-07-16 11:44:20.017 | GroqService API key: Set (length: 56)
2025-07-16 11:44:20.017 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:20.017 | GroqService ready state: Ready
2025-07-16 11:44:20.017 | GroqService is available and ready to use.
2025-07-16 11:44:20.017 | MistralService availability check: Available
2025-07-16 11:44:20.017 | MistralService API key: Set
2025-07-16 11:44:20.017 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:20.017 | MistralService is available and ready to use.
2025-07-16 11:44:20.017 | MistralService availability check: Available
2025-07-16 11:44:20.017 | MistralService API key: Set
2025-07-16 11:44:20.017 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 11:44:20.017 | MistralService is available and ready to use.
2025-07-16 11:44:20.017 | GroqService availability check: Available
2025-07-16 11:44:20.017 | GroqService API key: Set (length: 56)
2025-07-16 11:44:20.017 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 11:44:20.017 | GroqService ready state: Ready
2025-07-16 11:44:20.017 | GroqService is available and ready to use.
2025-07-16 11:44:20.017 | Model anthropic/claude-3-haiku-20240307 score calculation: base=30, adjusted=30, reliability=0, final=30
2025-07-16 11:44:20.017 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:20.017 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:20.017 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:20.017 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:20.017 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:20.017 | [Brain] Model anthropic/claude-2 failed: Connection error.
2025-07-16 11:44:20.017 | No active request found for request ID fe196a0d-8d63-4fe6-8260-56d9ea525082
2025-07-16 11:44:20.018 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:20.018 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 11:44:20.018 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:20.018 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:20.018 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:20.018 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:20.018 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 11:44:20.018 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 11:44:20.018 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 11:44:20.018 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 11:44:20.018 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 11:44:20.018 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 11:44:20.018 | [Brain Chat] Error: Connection error.