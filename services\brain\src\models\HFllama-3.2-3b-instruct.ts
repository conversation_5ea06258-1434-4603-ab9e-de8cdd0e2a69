import { BaseModel, ModelScore } from './baseModel';
import { LLMConversationType } from '../interfaces/baseInterface';

export class HFLlamaModel extends BaseModel {
    constructor() {
        const scoresByConversationType = new Map<LLMConversationType, ModelScore>([
            [LLMConversationType.TextToText, {
                costScore: 15,  // Lowered to avoid using this model when it returns 404
                accuracyScore: 35,  // Lowered to avoid using this model when it returns 404
                creativityScore: 30,  // Lowered to avoid using this model when it returns 404
                speedScore: 25  // Lowered to avoid using this model when it returns 404
            }]
        ]);

        super({
            name: "hf/meta-llama/llama-3.2-3b-instruct",
            modelName: "meta-llama/llama-3.2-3b-instruct",
            interfaceName: "huggingface",
            serviceName: "HFService",
            tokenLimit: 4096, // Adjust this value if needed
            scoresByConversationType: scoresByConversationType,
            contentConversation: [LLMConversationType.TextToText]
        });
    }
}

const aiModel = new HFLlamaModel();
export default aiModel;